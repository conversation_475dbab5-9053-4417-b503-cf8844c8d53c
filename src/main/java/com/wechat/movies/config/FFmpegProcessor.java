package com.wechat.movies.config;

import java.io.BufferedReader;
import java.io.InputStreamReader;

public class FFmpegProcessor {

    public static void processVideo(String input, String output) {
        try {
            // 使用兼容性更高的滤镜语法
            String videoFilter = 
                "noise=alls=30:allf=t," +
                "drawbox=enable='mod(t\\,0.1)':" +
                "x='if(eq(mod(n\\,10)\\,0)\\,rand(0\\,iw-100)\\,x)':" +  // 每10帧随机位置
                "y='if(eq(mod(n\\,10)\\,0)\\,rand(0\\,ih-100)\\,y)':" +
                "w=80:h=80:color=red@0.5";
            
            String[] cmd = {
                "ffmpeg",
                "-y",
                "-i", input,
                "-vf", videoFilter,
                // 简化音频处理（可选）
                "-af", "anoisesrc=amplitude=0.02",
                "-c:v", "libx264",
                "-preset", "fast",
                "-crf", "23",
                output
            };

            // 打印可执行的完整命令（方便调试）
            System.out.println("完整可执行命令:");
            StringBuilder fullCmd = new StringBuilder();
            for (String s : cmd) {
                fullCmd.append(s).append(" ");
            }
            System.out.println(fullCmd.toString());

            Process process = new ProcessBuilder(cmd).redirectErrorStream(true).start();
            
            // 实时输出日志
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("[FFmpeg] " + line);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg 失败，退出码: " + exitCode);
            }
            System.out.println("处理成功: " + output);
        } catch (Exception e) {
            throw new RuntimeException("FFmpeg 异常", e);
        }
    }

    public static void main(String[] args) {
        // 使用英文路径避免编码问题
        String input = "G:/视频/1/2.mp4";
        String output = "G:/视频/1/output_fixed.mp4";
        
        // 确保输入文件存在
        if (!new java.io.File(input).exists()) {
            System.err.println("输入文件不存在: " + input);
            return;
        }
        
        processVideo(input, output);
    }
}