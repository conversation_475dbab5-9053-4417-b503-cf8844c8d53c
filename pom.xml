<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.2</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.web</groupId>
    <artifactId>low-code</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>low-code</name>
    <description>低代码可拖拽生成页面的平台</description>

    <properties>
        <opencv.version>4.8.0-${javacv.version}</opencv.version>
        <os.platform>windows-x86_64</os.platform> <!-- 按系统修改 -->
        <java.version>17</java.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <druid.version>1.2.20</druid.version>
        <mysql.version>8.0.33</mysql.version>
        <springdoc.version>2.4.0</springdoc.version>
        <jjwt.version>0.11.5</jjwt.version>
        <javacv.version>1.5.9</javacv.version>

    </properties>

    <dependencies>
        <!-- Spring Boot 基础依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
            <version>1.5.9</version> <!-- 使用最新版本 -->
        </dependency>

        <!-- 显式添加必要模块 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg-platform</artifactId>
            <version>7.1.1-1.5.12</version>
        </dependency>

        <!-- 显式添加 FFmpeg 依赖（Windows 平台） -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <version>6.0-${javacv.version}</version>
            <classifier>windows-x86_64</classifier>
        </dependency>

        <!-- 显式添加 OpenBLAS 依赖（Windows 平台） -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>openblas</artifactId>
            <version>0.3.9-1.5.3</version>
            <classifier>windows-x86_64</classifier>
        </dependency>

        <!-- 显式添加 JavaCPP 依赖 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacpp</artifactId>
            <version>1.5.9</version>
            <classifier>windows-x86_64</classifier>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>xuggle-xuggler-repository</id>
            <url>http://www.xuggle.com/maven/</url>
        </repository>

    </repositories>

    <build>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
